AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  SAM Template for User Authentication System

# Global configuration
Globals:
  Function:
    Timeout: 15
    MemorySize: 128
    Environment:
      Variables:
        CORS_ALLOW_ORIGIN: 'https://authiqa.com'
        FRONTEND_URL: '{{resolve:ssm:/your-app/frontend-url}}'
  Api:
    Cors:
      AllowMethods: "'GET,POST,OPTIONS'"
      AllowHeaders: "'Content-Type,Authorization'"
      AllowOrigin: "'https://authiqa.com'"
      AllowCredentials: true

Resources:
  # Parameter Store for configuration
  AuthiqaParams:
    Type: AWS::SSM::Parameter
    Properties:
      Name: AUTHIQA_PARAMS
      Type: String
      Value: '{"USER_DETAILS_TABLE_NAME":"userAuthentication","BILLING_TABLE_NAME":"authiqaBilling","SMTP_HOST":"smtp.mail.eu-west-1.awsapps.com","SMTP_PORT":"465","SMTP_USER":"<EMAIL>","REGION":"eu-west-1","OTP_EXPIRY":"900000","NODE_ENV":"production","FRONTEND_URL":"https://authiqa.com","PAYMENT_HISTORY_TABLE_NAME":"paymentHistory","COST_CALCULATOR_LAMBDA_NAME":"costCalculatorLambda"}'
      Description: Configuration parameters for Authiqa application

  # Parameter Store for staging configuration
  StagingAuthiqaParams:
    Type: AWS::SSM::Parameter
    Properties:
      Name: staging-AUTHIQA_PARAMS
      Type: String
      Value: '{"USER_DETAILS_TABLE_NAME":"staging-userAuthentication","BILLING_TABLE_NAME":"staging-authiqaBilling","SMTP_HOST":"smtp.mail.eu-west-1.awsapps.com","SMTP_PORT":"465","SMTP_USER":"<EMAIL>","REGION":"eu-west-1","OTP_EXPIRY":"900000","NODE_ENV":"production","FRONTEND_URL":"https://staging.authiqa.com","PAYMENT_HISTORY_TABLE_NAME":"staging-paymentHistory","COST_CALCULATOR_LAMBDA_NAME":"costCalculatorLambda-staging", "GOOGLE_CLIENT_ID": "90602971467-p2a9ohurea4sqp15u75ltod41k9a0rci.apps.googleusercontent.com"}'
      Description: Configuration parameters for Authiqa staging environment

  GoogleAuthFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/src/googleAuthLambda/
      Handler: handler.googleAuthLambda
      Runtime: nodejs18.x
      Role: !GetAtt GoogleAuthFunctionRole.Arn
      Environment:
        Variables:
          USER_DETAILS_TABLE_NAME: !Ref UserDetailsTable
          NODE_ENV: 'production'
          GOOGLE_CLIENT_ID: '90602971467-p2a9ohurea4sqp15u75ltod41k9a0rci.apps.googleusercontent.com'
          GOOGLE_CLIENT_SECRET_NAME: !Ref GoogleClientSecretName
          ENCRYPTION_KEY: "dBG9kF7bH5cJ2mN8pQ4sT6wX0yZ3vA1eI/nL+uM9oR4="
      Events:
        GoogleAuthApi:
          Type: Api
          Properties:
            Path: /auth/google
            Method: POST
        GoogleAuthOptionsApi:
          Type: Api
          Properties:
            Path: /auth/google
            Method: OPTIONS

  SignUpFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/src/signUpLambda/
      Handler: handler.lambda
      Runtime: nodejs18.x
      Role: !GetAtt SignUpFunctionRole.Arn
      Environment:
        Variables:
          SMTP_HOST: 'smtp.mail.eu-west-1.awsapps.com'
          SMTP_PORT: '465'
          SMTP_USER: '<EMAIL>'
          SECRET_NAME: 'SMTP_PASSWORD_SECRET' # this is the secret name in the AWS Secrets Manager
          REGION: 'eu-west-1'
          USER_DETAILS_TABLE_NAME: !Ref UserDetailsTable
          OTP_EXPIRY: '900000'
          NODE_ENV: 'production'
          TELEGRAM_BOT_TOKEN: '**********************************************'
          TELEGRAM_CHAT_ID: '-4745314338'
          ENCRYPTION_KEY: "dBG9kF7bH5cJ2mN8pQ4sT6wX0yZ3vA1eI/nL+uM9oR4="
      Events:
        SignUpApi:
          Type: Api
          Properties:
            Path: /auth/signup
            Method: POST
        SignUpOptionsApi:
          Type: Api
          Properties:
            Path: /auth/signup
            Method: OPTIONS

  SignInFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/src/signInLambda/
      Handler: handler.lambda
      Runtime: nodejs18.x
      Role: !GetAtt SignInFunctionRole.Arn
      Environment:
        Variables:
          USER_DETAILS_TABLE_NAME: !Ref UserDetailsTable
          NODE_ENV: 'production'
      Events:
        SignInApi:
          Type: Api
          Properties:
            Path: /auth/signin
            Method: POST
        SignInOptionsApi:
          Type: Api
          Properties:
            Path: /auth/signin
            Method: OPTIONS
  AuthiqaBillingTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: authiqaBilling
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: publicKey
          AttributeType: S
        - AttributeName: monthYear
          AttributeType: S
        - AttributeName: costAssociatedWithAccounts
          AttributeType: N
        - AttributeName: costAssociatedWithIO
          AttributeType: N
        - AttributeName: totalAccounts
          AttributeType: N
        - AttributeName: totalFinalCost
          AttributeType: N
        - AttributeName: totalIOInteractions
          AttributeType: N
      KeySchema:
        - AttributeName: publicKey
          KeyType: HASH
        - AttributeName: monthYear
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: costAssociatedWithAccountsIndex
          KeySchema:
            - AttributeName: costAssociatedWithAccounts
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: costAssociatedWithIOIndex
          KeySchema:
            - AttributeName: costAssociatedWithIO
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: totalAccountsIndex
          KeySchema:
            - AttributeName: totalAccounts
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: totalFinalCostIndex
          KeySchema:
            - AttributeName: totalFinalCost
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: totalIOInteractionsIndex
          KeySchema:
            - AttributeName: totalIOInteractions
              KeyType: HASH
          Projection:
            ProjectionType: ALL
  UserDetailsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: userAuthentication
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userID
          AttributeType: S
        - AttributeName: email
          AttributeType: S
        - AttributeName: username
          AttributeType: S
        - AttributeName: OTP
          AttributeType: S
        - AttributeName: accountStatus
          AttributeType: S
        - AttributeName: createdAt
          AttributeType: N
        - AttributeName: loginAttempts
          AttributeType: N
        - AttributeName: lastLoginAttempt
          AttributeType: N
        - AttributeName: lockedUntil
          AttributeType: N
        - AttributeName: publicKey
          AttributeType: S
        - AttributeName: googleId
          AttributeType: S
        - AttributeName: parentAccount
          AttributeType: S
      KeySchema:
        - AttributeName: userID
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: emailIndex
          KeySchema:
            - AttributeName: email
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: usernameIndex
          KeySchema:
            - AttributeName: username
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: otpIndex
          KeySchema:
            - AttributeName: OTP
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: accountStatusIndex
          KeySchema:
            - AttributeName: accountStatus
              KeyType: HASH
            - AttributeName: createdAt
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: publicKeyIndex
          KeySchema:
            - AttributeName: publicKey
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: googleId-parentAccount-index
          KeySchema:
            - AttributeName: googleId
              KeyType: HASH
            - AttributeName: parentAccount
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      TimeToLiveSpecification:
        AttributeName: OTPExpiry
        Enabled: true

  SignUpFunctionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: SSMParameterAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/AUTHIQA_PARAMS'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/staging-AUTHIQA_PARAMS'
        - PolicyName: DynamoDBAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:PutItem
                  - dynamodb:GetItem
                  - dynamodb:Query
                  - dynamodb:Scan
                Resource: 
                  - !GetAtt UserDetailsTable.Arn
                  - !Sub "${UserDetailsTable.Arn}/index/*"
        - PolicyName: CloudWatchLogsAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource: arn:aws:logs:*:*:*
        - PolicyName: ParameterStoreAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                  - ssm:GetParameters
                Resource:
                  - arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/your-app/frontend-url

  SignInFunctionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: SSMParameterAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/AUTHIQA_PARAMS'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/staging-AUTHIQA_PARAMS'
        - PolicyName: DynamoDBAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:GetItem
                  - dynamodb:UpdateItem
                Resource: 
                  - !GetAtt UserDetailsTable.Arn
                  - !Sub "${UserDetailsTable.Arn}/index/*"
        - PolicyName: CloudWatchLogsAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource: arn:aws:logs:*:*:*
        - PolicyName: ParameterStoreAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                  - ssm:GetParameters
                Resource:
                  - arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/your-app/telegram-chat-id
                  - arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/your-app/frontend-url

  JWTSecret:
    Type: AWS::SSM::Parameter
    Properties:
      Name: /myapp/jwt-secret
      Type: String
      Value: your-secret-key-here
      Description: JWT Secret for sign-in and sign-up functions
  EmailConfirmationFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/src/emailConfirmationLambda/
      Handler: handler.lambda
      Runtime: nodejs18.x
      Events:
        EmailConfirmation:
          Type: Api
          Properties:
            Path: /auth/confirm-email
            Method: get
            Cors:
              AllowMethods: "'GET, OPTIONS'"
              AllowHeaders: "'Content-Type,Authorization'"
              AllowOrigin: "'https://authiqa.com'"
              AllowCredentials: true
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref UserDetailsTable
        - CloudWatchLogsPolicy: {}
        - PolicyName: SSMParameterAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/AUTHIQA_PARAMS'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/staging-AUTHIQA_PARAMS'
        - PolicyName: ParameterStoreAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                  - ssm:GetParameters
                Resource:
                  - arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/your-app/telegram-chat-id
                  - arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/your-app/frontend-url
      Environment:
        Variables:
          USER_DETAILS_TABLE_NAME: !Ref UserDetailsTable
          FRONTEND_URL: '{{resolve:ssm:/your-app/frontend-url}}'
          NODE_ENV: 'production'

  ResendConfirmationEmailFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/src/resendConfirmationEmailLambda/
      Handler: handler.lambda
      Runtime: nodejs18.x
      Role: !GetAtt SignUpFunctionRole.Arn
      Events:
        ResendConfirmationEmail:
          Type: Api
          Properties:
            Path: /auth/request-new-confirmation
            Method: post
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref UserDetailsTable
        - CloudWatchLogsPolicy: {}
        - PolicyName: SSMParameterAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/AUTHIQA_PARAMS'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/staging-AUTHIQA_PARAMS'
      Environment:
        Variables:
          SMTP_HOST: 'smtp.mail.eu-west-1.awsapps.com'  # WorkMail endpoint
          SMTP_PORT: '465'  # SSL port
          SMTP_USER: '<EMAIL>'
          SECRET_NAME: 'SMTP_PASSWORD_SECRET'  # this is the secret name in the AWS Secrets Manager
          REGION: 'eu-west-1'
          USER_DETAILS_TABLE_NAME: !Ref UserDetailsTable
          OTP_COOLDOWN_PERIOD: '300000'  # 5 minutes in milliseconds
          OTP_EXPIRY: '900000'  # 15 minutes in milliseconds
          FRONTEND_URL: '{{resolve:ssm:/your-app/frontend-url}}'
          NODE_ENV: 'production'
          ENCRYPTION_KEY: "dBG9kF7bH5cJ2mN8pQ4sT6wX0yZ3vA1eI/nL+uM9oR4="  # Added for JWT verification

  ResetPasswordFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/src/resetPasswordLambda/
      Handler: handler.lambda
      Runtime: nodejs18.x
      Role: !GetAtt SignUpFunctionRole.Arn
      Events:
        ResetPassword:
          Type: Api
          Properties:
            Path: /auth/reset-password
            Method: post
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref UserDetailsTable
        - CloudWatchLogsPolicy: {}
        - PolicyName: SSMParameterAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/AUTHIQA_PARAMS'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/staging-AUTHIQA_PARAMS'
      Environment:
        Variables:
          SMTP_HOST: 'smtp.mail.eu-west-1.awsapps.com'  # WorkMail endpoint
          SMTP_PORT: '465'  # SSL port
          SMTP_USER: '<EMAIL>'
          SECRET_NAME: 'SMTP_PASSWORD_SECRET' # this is the secret name in the AWS Secrets Manager
          REGION: 'eu-west-1'
          USER_DETAILS_TABLE_NAME: !Ref UserDetailsTable
          OTP_COOLDOWN_PERIOD: '300000'  # 5 minutes in milliseconds
          OTP_EXPIRY: '900000'  # 15 minutes in milliseconds
          FRONTEND_URL: '{{resolve:ssm:/your-app/frontend-url}}'
          NODE_ENV: 'production'
          ENCRYPTION_KEY: "dBG9kF7bH5cJ2mN8pQ4sT6wX0yZ3vA1eI/nL+uM9oR4="  # Generate this using the command below

  UpdatePasswordFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/src/updatePasswordLambda/
      Handler: handler.lambda
      Runtime: nodejs18.x
      Role: !GetAtt SignUpFunctionRole.Arn
      Events:
        UpdatePassword:
          Type: Api
          Properties:
            Path: /auth/update-password
            Method: post
            Cors:
              AllowMethods: "'POST, OPTIONS'"
              AllowHeaders: "'Content-Type,Authorization'"
              AllowOrigin: "'https://authiqa.com'"
              AllowCredentials: true
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref UserDetailsTable
        - CloudWatchLogsPolicy: {}
        - PolicyName: SSMParameterAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/AUTHIQA_PARAMS'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/staging-AUTHIQA_PARAMS'
      Environment:
        Variables:
          USER_DETAILS_TABLE_NAME: !Ref UserDetailsTable
          FRONTEND_URL: '{{resolve:ssm:/your-app/frontend-url}}'
          JWT_SECRET: !Ref JwtSecret
          NODE_ENV: 'production'
          ENCRYPTION_KEY: "dBG9kF7bH5cJ2mN8pQ4sT6wX0yZ3vA1eI/nL+uM9oR4="

  UpdateOrganizationFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/src/updateOrganizationLambda/
      Handler: handler.updateOrganizationLambda
      Runtime: nodejs18.x
      Role: !GetAtt SignUpFunctionRole.Arn
      Events:
        UpdateOrganization:
          Type: Api
          Properties:
            Path: /auth/update-organization
            Method: post
            Cors:
              AllowMethods: "'POST, OPTIONS'"
              AllowHeaders: "'Content-Type,Authorization'"
              AllowOrigin: "'https://authiqa.com'"
              AllowCredentials: true
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref UserDetailsTable
        - CloudWatchLogsPolicy: {}
        - PolicyName: SSMParameterAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/AUTHIQA_PARAMS'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/staging-AUTHIQA_PARAMS'
      Environment:
        USER_DETAILS_TABLE_NAME: !Ref UserDetailsTable
        FRONTEND_URL: '{{resolve:ssm:/your-app/frontend-url}}'
        NODE_ENV: 'production'

  CostCalculatorLambda:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/costCalculatorLambda/
      Handler: handler.costCalculatorLambda
      Runtime: nodejs18.x
      Timeout: 15
      Environment:
        Variables:
          BILLING_TABLE_NAME: !Ref AuthiqaBillingTable
          USER_DETAILS_TABLE_NAME: !Ref UserDetailsTable
          NODE_ENV: 'production'
          INTERNAL_SERVICE_KEY: !Ref InternalServiceKey
          SMTP_HOST: 'smtp.mail.eu-west-1.awsapps.com'
          SMTP_PORT: '465'
          SMTP_USER: '<EMAIL>'
          SECRET_NAME: 'SMTP_PASSWORD_SECRET'
          REGION: 'eu-west-1'
          FRONTEND_URL: '{{resolve:ssm:/your-app/frontend-url}}'
      Events:
        GetCostAnalysis:
          Type: Api 
          Properties:
            Path: /parent/cost-analysis
            Method: GET
            RestApiId: !Ref MainApi
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref UserDetailsTable
        - DynamoDBCrudPolicy:
            TableName: !Ref AuthiqaBillingTable
        - SecretsManagerReadPolicy:
            SecretArn: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:SMTP_PASSWORD_SECRET*'
        - PolicyName: SSMParameterAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/AUTHIQA_PARAMS'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/staging-AUTHIQA_PARAMS'
MonthlyBillingStorageFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/src/monthlyBillingStorageLambda/
      Handler: handler.monthlyBillingStorageLambda
      Runtime: nodejs18.x
      Role: !GetAtt BillingFunctionRole.Arn
      Environment:
        Variables:
          BILLING_TABLE_NAME: !Ref AuthiqaBillingTable
          USER_DETAILS_TABLE_NAME: !Ref UserDetailsTable
          NODE_ENV: 'production'
          INTERNAL_SERVICE_KEY: !Ref InternalServiceKey
          ENVIRONMENT: 'live'
      Events:
        MonthlyBillingTrigger:
          Type: Schedule
          Properties:
            Schedule: cron(0 0 1 * ? *)
            Name: MonthlyBillingStorageTrigger
            Description: Triggers monthly billing storage on the 1st of each month
            Enabled: true

  BillingHistoryFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/src/billingHistoryLambda/
      Handler: handler.billingHistoryLambda
      Runtime: nodejs18.x
      Role: !GetAtt BillingFunctionRole.Arn
      Environment:
        Variables:
          BILLING_TABLE_NAME: !Ref AuthiqaBillingTable
          NODE_ENV: 'production'
      Events:
        GetBillingHistory:
          Type: Api
          Properties:
            Path: /parent/billing-history
            Method: GET
            RestApiId: !Ref MainApi

  BillingFunctionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: SSMParameterAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/AUTHIQA_PARAMS'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/staging-AUTHIQA_PARAMS'
        - PolicyName: DynamoDBAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:PutItem
                  - dynamodb:GetItem
                  - dynamodb:Query
                  - dynamodb:Scan
                Resource: 
                  - !GetAtt AuthiqaBillingTable.Arn
                  - !GetAtt UserDetailsTable.Arn
                  - !Sub "${AuthiqaBillingTable.Arn}/index/*"
                  - !Sub "${UserDetailsTable.Arn}/index/*"
        - PolicyName: CloudWatchLogsAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource: arn:aws:logs:*:*:*
        - PolicyName: LambdaInvokeAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - lambda:InvokeFunction
                Resource: !GetAtt CostCalculatorLambda.Arn
        - PolicyName: ParameterStoreAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                  - ssm:GetParameters
                Resource:
                  - arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/your-app/telegram-chat-id
                  - arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/your-app/frontend-url

  PaymentHistoryTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: paymentHistory
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: paymentId
          AttributeType: S
        - AttributeName: userID
          AttributeType: S
        - AttributeName: apiKey
          AttributeType: S
        - AttributeName: createdAt
          AttributeType: N
        - AttributeName: status
          AttributeType: S
      KeySchema:
        - AttributeName: paymentId
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: userID-createdAt-index
          KeySchema:
            - AttributeName: userID
              KeyType: HASH
            - AttributeName: createdAt
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: apiKey-createdAt-index
          KeySchema:
            - AttributeName: apiKey
              KeyType: HASH
            - AttributeName: createdAt
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: status-createdAt-index
          KeySchema:
            - AttributeName: status
              KeyType: HASH
            - AttributeName: createdAt
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

  PaymentLambdaRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: SSMParameterAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/AUTHIQA_PARAMS'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/staging-AUTHIQA_PARAMS'
        - PolicyName: PaymentTableAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:PutItem
                  - dynamodb:GetItem
                  - dynamodb:Query
                  - dynamodb:UpdateItem
                Resource: 
                  - !GetAtt PaymentHistoryTable.Arn
                  - !Sub "${PaymentHistoryTable.Arn}/index/*"
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:STRIPE_SECRETS*'
        - PolicyName: CloudWatchLogsAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource: arn:aws:logs:*:*:*
        - PolicyName: ParameterStoreAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                  - ssm:GetParameters
                Resource:
                  - arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/your-app/telegram-chat-id
                  - arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/your-app/frontend-url

  PaymentHistoryFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/src/paymentHistoryLambda
      Handler: handler.paymentHistoryLambda
      Runtime: nodejs18.x
      MemorySize: 256
      Timeout: 30
      Environment:
        Variables:
          PAYMENT_HISTORY_TABLE_NAME: !Ref PaymentHistoryTable
          USER_DETAILS_TABLE_NAME: !Ref UserDetailsTable
          ENCRYPTION_KEY: !Ref EncryptionKey
          JWT_SECRET: !Ref JwtSecret
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref PaymentHistoryTable
        - DynamoDBReadPolicy:
            TableName: !Ref UserDetailsTable
      Events:
        GetPaymentHistory:
          Type: Api
          Properties:
            Path: /parent/payment-history
            Method: GET
            RestApiId: !Ref MainApi
            Auth:
              Authorizer: MainAuthorizer

  StripeWebhookFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/src/stripeWebhookLambda/
      Handler: handler.stripeWebhookLambda
      Runtime: nodejs18.x
      Role: !GetAtt PaymentLambdaRole.Arn
      Environment:
        Variables:
          # Payment related
          PAYMENT_HISTORY_TABLE_NAME: !Ref PaymentHistoryTable
          USER_DETAILS_TABLE_NAME: !Ref UserDetailsTable
          STRIPE_SECRETS_NAME: 'STRIPE_SECRETS'
          # Email related
          SMTP_HOST: 'smtp.mail.eu-west-1.awsapps.com'
          SMTP_PORT: '465'
          SMTP_USER: '<EMAIL>'
          SECRET_NAME: 'SMTP_PASSWORD_SECRET'
          # General config
          REGION: 'eu-west-1'
          FRONTEND_URL: '{{resolve:ssm:/your-app/frontend-url}}'
          NODE_ENV: 'production'
          CHROME_PATH: /opt/chrome/chrome
          PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: true
      Events:
        StripeWebhook:
          Type: Api
          Properties:
            Path: /webhooks/stripe
            Method: POST
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref PaymentHistoryTable
        - DynamoDBReadPolicy:
            TableName: !Ref UserDetailsTable
        - CloudWatchLogsPolicy: {}
      Layers:
        - !Sub arn:aws:lambda:${AWS::Region}:764866452798:layer:chrome-aws-lambda:latest
      MemorySize: 2048  # Sufficient for PDF generation
      Timeout: 60       # Adequate timeout

  PaymentInitializationFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/src/paymentInitializationLambda/
      Handler: handler.paymentInitializationLambda
      Runtime: nodejs18.x
      Role: !GetAtt PaymentLambdaRole.Arn
      Environment:
        Variables:
          PAYMENT_HISTORY_TABLE_NAME: !Ref PaymentHistoryTable
          USER_DETAILS_TABLE_NAME: !Ref UserDetailsTable
          STRIPE_SECRETS_NAME: 'STRIPE_SECRETS'
      Events:
        InitializePayment:
          Type: Api
          Properties:
            Path: /parent/initialize-payment
            Method: POST
            RestApiId: !Ref MainApi
            Auth:
              Authorizer: MainAuthorizer

  PromoteAccountFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/src/promoteAccountLambda/
      Handler: handler.promoteAccountLambda
      Runtime: nodejs18.x
      Role: !GetAtt UserManagementFunctionRole.Arn
      Environment:
        Variables:
          USER_DETAILS_TABLE_NAME: !Ref UserDetailsTable
          FRONTEND_URL: '{{resolve:ssm:/your-app/frontend-url}}'
          JWT_SECRET: !Ref JwtSecret
          NODE_ENV: 'production'
      Events:
        PromoteAccountApi:
          Type: Api
          Properties:
            Path: /auth/promote-account
            Method: POST
        PromoteAccountOptionsApi:
          Type: Api
          Properties:
            Path: /auth/promote-account
            Method: OPTIONS

  GoogleAuthFunctionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: SSMParameterAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/AUTHIQA_PARAMS'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/staging-AUTHIQA_PARAMS'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/myapp/google-client-secret'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/myapp/staging-google-client-secret'
        - PolicyName: DynamoDBAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:PutItem
                  - dynamodb:GetItem
                  - dynamodb:UpdateItem
                  - dynamodb:Query
                Resource: 
                  - !GetAtt UserDetailsTable.Arn
                  - !Sub "${UserDetailsTable.Arn}/index/*"
        - PolicyName: CloudWatchLogsAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource: arn:aws:logs:*:*:*

  GoogleClientSecret:
    Type: AWS::SSM::Parameter
    Properties:
      Name: /myapp/google-client-secret
      Type: String
      Value: 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
      Description: Google Client Secret for production
  StagingGoogleClientSecret:
    Type: AWS::SSM::Parameter
    Properties:
      Name: /myapp/staging-google-client-secret
      Type: String
      Value: 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 # Replace with staging secret
      Description: Google Client Secret for staging

Parameters:
  FrontendUrl:
    Type: String
    Description: Frontend application URL
    Default: https://authiqa.com
  JwtSecret:
    Type: String
    Description: JWT secret key for authentication
    NoEcho: true
  GoogleClientSecretName:
    Type: String
    Description: "The name of the SSM parameter for Google Client Secret"
    Default: /myapp/google-client-secret
  SmtpPassword:
    Type: String
    Description: SMTP password
    NoEcho: true
  InternalServiceKey:
    Type: String
    Description: Internal service key for lambda-to-lambda communication
    NoEcho: true
  TelegramChatId:
    Type: String
    Description: Telegram chat ID for signup notifications
    Default: '-4745314338'
