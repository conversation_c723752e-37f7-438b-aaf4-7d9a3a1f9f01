import { APIGatewayProxyEvent } from 'aws-lambda';
import { getUserProfileLambda } from '../handler';
import { verifyToken } from '../../shared/utils/tokenUtils';
import { getUserByEmail } from '../../shared/database/userOperations';
import { getConfig } from '../../shared/services/configService';

// Mock dependencies
jest.mock('../../shared/utils/tokenUtils');
jest.mock('../../shared/database/userOperations');
jest.mock('../../shared/services/configService');

const mockVerifyToken = verifyToken as jest.MockedFunction<typeof verifyToken>;
const mockGetUserByEmail = getUserByEmail as jest.MockedFunction<typeof getUserByEmail>;
const mockGetConfig = getConfig as jest.MockedFunction<typeof getConfig>;

describe('getUserProfileLambda', () => {
  const mockConfig = {
    USER_DETAILS_TABLE_NAME: 'test-userAuthentication',
    NODE_ENV: 'test',
    FRONTEND_URL: 'https://test.authiqa.com'
  };

  const mockUser = {
    userID: 'test-user-id',
    username: 'testuser',
    email: '<EMAIL>',
    publicKey: 'test-public-key',
    accountType: 'parent' as const,
    parentAccount: 'ROOT',
    accountStatus: 'active' as const,
    emailVerified: true,
    createdAt: Date.now(),
    organizationName: 'Test Org',
    organizationUrl: 'https://test.com',
    authProvider: 'email' as const,
    accountBalance: 100,
    availableBalance: 90,
    organizationUpdateCount: 5,
    signInCount: 10
  };

  const createMockEvent = (authHeader?: string): APIGatewayProxyEvent => ({
    headers: {
      Authorization: authHeader || '',
      Host: 'api.authiqa.com'
    },
    body: null,
    httpMethod: 'GET',
    path: '/auth/user-profile',
    queryStringParameters: null,
    pathParameters: null,
    stageVariables: null,
    requestContext: {} as any,
    resource: '',
    isBase64Encoded: false,
    multiValueHeaders: {},
    multiValueQueryStringParameters: null
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetConfig.mockResolvedValue(mockConfig as any);
  });

  describe('Authentication', () => {
    it('should return 401 when no authorization header is provided', async () => {
      const event = createMockEvent();
      const result = await getUserProfileLambda(event);

      expect(result.statusCode).toBe(401);
      expect(JSON.parse(result.body)).toMatchObject({
        error: expect.objectContaining({
          code: 'UNAUTHORIZED'
        })
      });
    });

    it('should return 401 when token verification fails', async () => {
      const event = createMockEvent('Bearer invalid-token');
      mockVerifyToken.mockResolvedValue(null);

      const result = await getUserProfileLambda(event);

      expect(result.statusCode).toBe(401);
      expect(mockVerifyToken).toHaveBeenCalledWith('invalid-token', 'test-userAuthentication');
    });

    it('should return 404 when user is not found', async () => {
      const event = createMockEvent('Bearer valid-token');
      mockVerifyToken.mockResolvedValue({
        userID: 'test-user-id',
        email: '<EMAIL>',
        accountType: 'parent'
      });
      mockGetUserByEmail.mockResolvedValue(null);

      const result = await getUserProfileLambda(event);

      expect(result.statusCode).toBe(404);
      expect(mockGetUserByEmail).toHaveBeenCalledWith('<EMAIL>', 'test-userAuthentication');
    });

    it('should return 403 when account is not active', async () => {
      const event = createMockEvent('Bearer valid-token');
      mockVerifyToken.mockResolvedValue({
        userID: 'test-user-id',
        email: '<EMAIL>',
        accountType: 'parent'
      });
      mockGetUserByEmail.mockResolvedValue({
        ...mockUser,
        accountStatus: 'inactive'
      } as any);

      const result = await getUserProfileLambda(event);

      expect(result.statusCode).toBe(403);
    });
  });

  describe('Successful Profile Retrieval', () => {
    it('should return complete user profile for parent account', async () => {
      const event = createMockEvent('Bearer valid-token');
      mockVerifyToken.mockResolvedValue({
        userID: 'test-user-id',
        email: '<EMAIL>',
        accountType: 'parent'
      });
      mockGetUserByEmail.mockResolvedValue(mockUser as any);

      const result = await getUserProfileLambda(event);

      expect(result.statusCode).toBe(200);
      const responseBody = JSON.parse(result.body);
      
      expect(responseBody.message).toBe('User profile retrieved successfully');
      expect(responseBody.user).toMatchObject({
        userID: 'test-user-id',
        username: 'testuser',
        email: '<EMAIL>',
        publicKey: 'test-public-key',
        accountType: 'parent',
        parentAccount: 'ROOT',
        accountStatus: 'active',
        emailVerified: true,
        organizationName: 'Test Org',
        organizationUrl: 'https://test.com',
        authProvider: 'email',
        accountBalance: 100,
        availableBalance: 90,
        organizationUpdateCount: 5,
        signInCount: 10
      });
    });

    it('should return profile for child account without billing info', async () => {
      const childUser = {
        ...mockUser,
        accountType: 'child' as const,
        parentAccount: 'parent-public-key',
        accountBalance: undefined,
        availableBalance: undefined
      };

      const event = createMockEvent('Bearer valid-token');
      mockVerifyToken.mockResolvedValue({
        userID: 'test-user-id',
        email: '<EMAIL>',
        accountType: 'child'
      });
      mockGetUserByEmail.mockResolvedValue(childUser as any);

      const result = await getUserProfileLambda(event);

      expect(result.statusCode).toBe(200);
      const responseBody = JSON.parse(result.body);
      
      expect(responseBody.user.accountType).toBe('child');
      expect(responseBody.user.parentAccount).toBe('parent-public-key');
      expect(responseBody.user.accountBalance).toBeUndefined();
      expect(responseBody.user.availableBalance).toBeUndefined();
    });

    it('should handle promoted account information', async () => {
      const promotedUser = {
        ...mockUser,
        isPromotedAccount: true,
        promotedAt: Date.now(),
        organizationId: 'special-org-id'
      };

      const event = createMockEvent('Bearer valid-token');
      mockVerifyToken.mockResolvedValue({
        userID: 'test-user-id',
        email: '<EMAIL>',
        accountType: 'parent'
      });
      mockGetUserByEmail.mockResolvedValue(promotedUser as any);

      const result = await getUserProfileLambda(event);

      expect(result.statusCode).toBe(200);
      const responseBody = JSON.parse(result.body);
      
      expect(responseBody.user.isPromotedAccount).toBe(true);
      expect(responseBody.user.promotedAt).toBeDefined();
      expect(responseBody.user.organizationId).toBe('special-org-id');
    });
  });

  describe('Environment Handling', () => {
    it('should work with staging environment', async () => {
      const stagingConfig = {
        ...mockConfig,
        USER_DETAILS_TABLE_NAME: 'staging-userAuthentication'
      };
      mockGetConfig.mockResolvedValue(stagingConfig as any);

      const event = createMockEvent('Bearer valid-token');
      event.headers.Host = 'staging.api.authiqa.com';
      
      mockVerifyToken.mockResolvedValue({
        userID: 'test-user-id',
        email: '<EMAIL>',
        accountType: 'parent'
      });
      mockGetUserByEmail.mockResolvedValue(mockUser as any);

      const result = await getUserProfileLambda(event);

      expect(result.statusCode).toBe(200);
      expect(mockGetUserByEmail).toHaveBeenCalledWith('<EMAIL>', 'staging-userAuthentication');
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      const event = createMockEvent('Bearer valid-token');
      mockVerifyToken.mockResolvedValue({
        userID: 'test-user-id',
        email: '<EMAIL>',
        accountType: 'parent'
      });
      mockGetUserByEmail.mockRejectedValue(new Error('Database connection failed'));

      const result = await getUserProfileLambda(event);

      expect(result.statusCode).toBe(500);
      const responseBody = JSON.parse(result.body);
      expect(responseBody.error.code).toBe('INTERNAL_SERVER_ERROR');
    });

    it('should show detailed errors in local environment', async () => {
      const localConfig = { ...mockConfig, NODE_ENV: 'local' };
      mockGetConfig.mockResolvedValue(localConfig as any);

      const event = createMockEvent('Bearer valid-token');
      mockVerifyToken.mockResolvedValue({
        userID: 'test-user-id',
        email: '<EMAIL>',
        accountType: 'parent'
      });
      mockGetUserByEmail.mockRejectedValue(new Error('Specific database error'));

      const result = await getUserProfileLambda(event);

      expect(result.statusCode).toBe(500);
      const responseBody = JSON.parse(result.body);
      expect(responseBody.error.message).toContain('Specific database error');
    });
  });
});
