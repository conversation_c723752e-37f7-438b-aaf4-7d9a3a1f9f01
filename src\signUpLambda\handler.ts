import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ErrorResponse, SuccessResponse } from '../shared/responseUtils';
import { validateSignUpInput } from '../shared/validation/userValidation';
import { Environment } from '../shared/services/environment';
import { addCorsHeaders } from '../shared/corsHandler';
import { UserService } from './services/userService';
import { ParentAccountService } from './services/parentAccountService';
import { EmailService } from './services/emailService';
import { NotificationService } from '../shared/services/notificationService';
import { getConfig } from '../shared/services/configService';
import { generateToken } from '../shared/utils/tokenUtils';
import { getUserByPublicKey } from '../shared/database/userOperations';

interface SignUpRequest {
  username: string;
  email: string;
  password: string;
  parentPublicKey?: string;
  verifyAuthPath?: string;
}

/**
 * Validates the request body and extracts the required fields
 */
function validateRequestBody(body: string | null): SignUpRequest {
  if (!body) {
    throw ErrorResponse(400, 'MISSING_REQUEST_BODY', 'Request body is required');
  }

  let requestBody;
  try {
    requestBody = JSON.parse(body);
  } catch {
    throw ErrorResponse(400, 'INVALID_REQUEST_BODY', 'Invalid JSON in request body');
  }

  const { username, email, password, parentPublicKey, verifyAuthPath } = requestBody;
  
  if (!email || !password) {
    throw ErrorResponse(400, 'MISSING_REQUIRED_FIELDS', 'Email and password are required');
  }

  return { username, email, password, parentPublicKey, verifyAuthPath };
}

/**
 * Validates the parent account if a parentPublicKey is provided
 */
async function validateParentAccount(parentPublicKey?: string, tableName?: string): Promise<void> {
  if (parentPublicKey) {
    console.log(`Validating parent account with key: ${parentPublicKey} using table: ${tableName || 'default'}`);
    const parentValidation = await ParentAccountService.validateParentPublicKey(parentPublicKey, tableName);
    if (!parentValidation.isValid) {
      throw parentValidation.error!;
    }
  }
}

/**
 * Prepares the success response data
 */
function prepareSuccessResponse(user: any, verificationToken: string): any {
  return {
    data: {
      userID: user.userID,
      username: user.username,
      email: user.email,
      createdAt: user.createdAt,
      publicKey: user.publicKey,
      emailVerified: false,
      parentAccount: user.parentAccount,
      accountType: user.accountType,
      ...(Environment.isLocal() && {
        localDevelopment: {
          verificationCode: verificationToken
        }
      })
    }
  };
}

export const signUpLambda = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  console.log('[SIGNUP] Lambda invoked with headers:', {
    host: event.headers.Host || event.headers.host,
    origin: event.headers.Origin || event.headers.origin
  });

  // Load configuration from the new service
  const config = await getConfig(event);
  
  console.log('[CONFIG] SignUpLambda using configuration service', {
    environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
    frontendUrl: config.FRONTEND_URL,
    userTable: config.USER_DETAILS_TABLE_NAME,
    nodeEnv: config.NODE_ENV
  });

  
  try {
    // Input Validation
    const { username, email, password, parentPublicKey, verifyAuthPath } = validateRequestBody(event.body);
    
    
    console.log('SignUp Request received with paths:', {
      verifyAuthPath,
      email // logging email to track the flow
    });

    // Validate input
    const validationResult = await validateSignUpInput({ username, email, password, parentPublicKey });
    if (!validationResult.isValid && validationResult.error) {
      throw ErrorResponse(400, validationResult.error.code, validationResult.error.message);
    }

    // Parent Account Validation - Pass the table name from config
    await validateParentAccount(parentPublicKey, config.USER_DETAILS_TABLE_NAME);

    // User Creation - Pass the table name from config
    const { user, verificationToken } = await UserService.createUser({
      username,
      email,
      password,
      parentPublicKey
    }, config.USER_DETAILS_TABLE_NAME);

    // Check if email verification is required for this organization
    let shouldSendVerificationEmail = false;  // Default to false
    if (parentPublicKey) {
      // For child accounts, check parent's email verification setting
      const parentUser = await getUserByPublicKey(parentPublicKey, config.USER_DETAILS_TABLE_NAME);
      shouldSendVerificationEmail = parentUser?.emailVerificationRequired === true;  // Only send if explicitly true
    } else {
      // For parent accounts, default to false (can be changed later via update organization)
      shouldSendVerificationEmail = false;
    }

    // Conditionally send verification email
    if (shouldSendVerificationEmail) {
      const emailResult = await EmailService.sendSignupVerification(
        email,
        verificationToken,
        parentPublicKey ? 'child' : 'parent',
        parentPublicKey,
        verifyAuthPath,
        event
      );

      if (!emailResult.success && emailResult.error) {
        return addCorsHeaders(emailResult.error, event);
      }
    }

    // Generate JWT token for immediate signin
    console.log(`[SIGNUP] Generating token for new user: ${user.userID}, account type: ${user.accountType}`);
    const token = await generateToken({
      userID: user.userID,
      email: user.email,
      accountType: user.accountType,
      publicKey: user.publicKey,
      parentAccount: user.parentAccount
    }, config.USER_DETAILS_TABLE_NAME);

    // Send Telegram notification (non-blocking)
    NotificationService.sendSignupNotification(
      username || 'unnamed', 
      email,
      event
    ).catch(err => {
      console.error('Error sending signup notification:', err);
    });

    // Success Response
    return addCorsHeaders(SuccessResponse(200, {
      token,
      data: {
        userID: user.userID,
        username: user.username,
        email: user.email,
        createdAt: user.createdAt,
        publicKey: user.publicKey,
        emailVerified: false,
        parentAccount: user.parentAccount,
        accountType: user.accountType,
        emailVerificationSent: shouldSendVerificationEmail,
        ...(config.NODE_ENV === 'local' && {
          localDevelopment: {
            verificationCode: verificationToken
          }
        })
      }
    }), event);
    

  } catch (error: unknown) {
    console.error('Error in signUpLambda:', error);
    
    if (error && 
        typeof error === 'object' && 
        'statusCode' in error && 
        'body' in error) {
      return addCorsHeaders(error as APIGatewayProxyResult, event);
    }
    
    // For unexpected errors, use config to determine if we should show detailed errors
    const errorMessage = config.NODE_ENV === 'local' 
      ? `${error instanceof Error ? error.message : 'An internal server error occurred'}` 
      : 'An internal server error occurred';
    
    return addCorsHeaders(ErrorResponse(500, 'INTERNAL_SERVER_ERROR', errorMessage), event);
  }
}; 
