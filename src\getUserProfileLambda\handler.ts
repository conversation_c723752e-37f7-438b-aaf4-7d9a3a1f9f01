import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ErrorTypes } from '../shared/errorTypes';
import { SuccessResponse } from '../shared/responseUtils';
import { getUserByEmail } from '../shared/database/userOperations';
import { addCorsHeaders } from '../shared/corsHandler';
import { verifyToken } from '../shared/utils/tokenUtils';
import { getConfig } from '../shared/services/configService';

export const getUserProfileLambda = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  // Load configuration from the service
  const config = await getConfig(event);
  
  console.log('[GET-USER-PROFILE] Lambda invoked with headers:', {
    host: event.headers.Host || event.headers.host,
    origin: event.headers.Origin || event.headers.origin
  });
  
  console.log('[CONFIG] GetUserProfileLambda using configuration service', {
    environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
    userTable: config.USER_DETAILS_TABLE_NAME
  });

  try {
    // Validate JWT authentication
    const authHeader = event.headers.Authorization || event.headers.authorization;
    if (!authHeader) {
      console.log('[GET-USER-PROFILE] Missing authorization header');
      return addCorsHeaders(ErrorTypes.UNAUTHORIZED(), event);
    }

    const token = authHeader.replace('Bearer ', '');
    const decodedToken = await verifyToken(token, config.USER_DETAILS_TABLE_NAME);
    if (!decodedToken) {
      console.log('[GET-USER-PROFILE] Invalid or expired token');
      return addCorsHeaders(ErrorTypes.UNAUTHORIZED(), event);
    }

    // Get complete user data
    const user = await getUserByEmail(decodedToken.email, config.USER_DETAILS_TABLE_NAME);
    if (!user) {
      console.log(`[GET-USER-PROFILE] User not found for email: ${decodedToken.email}`);
      return addCorsHeaders(ErrorTypes.USER_NOT_FOUND(), event);
    }

    // Validate account status
    if (user.accountStatus !== 'active') {
      console.log(`[GET-USER-PROFILE] Account ${user.userID} is not active: ${user.accountStatus}`);
      return addCorsHeaders(ErrorTypes.ACCOUNT_INACTIVE(), event);
    }

    console.log(`[GET-USER-PROFILE] Successfully retrieved profile for user: ${user.userID}`);

    // Return complete user profile data
    return addCorsHeaders(SuccessResponse(200, {
      message: 'User profile retrieved successfully',
      user: {
        // Core Identity
        userID: user.userID,
        username: user.username,
        email: user.email,
        publicKey: user.publicKey,
        
        // Account Information
        accountType: user.accountType,
        parentAccount: user.parentAccount,
        accountStatus: user.accountStatus,
        emailVerified: user.emailVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        lastLogin: user.lastLogin,
        
        // Organization Details
        organizationName: user.organizationName || null,
        organizationUrl: user.organizationUrl || null,
        authUrls: user.authUrls || null,
        domainRestrictionEnabled: user.domainRestrictionEnabled !== undefined ? user.domainRestrictionEnabled : true,
        emailVerificationRequired: user.emailVerificationRequired !== undefined ? user.emailVerificationRequired : false,
        
        // Promoted Account Information
        isPromotedAccount: user.isPromotedAccount || false,
        promotedAt: user.promotedAt || null,
        organizationId: user.organizationId || null,
        
        // Authentication Details
        authProvider: user.authProvider || 'email',
        lastLoginProvider: user.lastLoginProvider || null,
        googleId: user.googleId || null,
        
        // Billing Information (for parent accounts)
        ...(user.accountType === 'parent' && {
          accountBalance: user.accountBalance || 0,
          availableBalance: user.availableBalance || 0
        }),
        
        // Operation Counters
        organizationUpdateCount: user.organizationUpdateCount || 0,
        emailConfirmationCount: user.emailConfirmationCount || 0,
        resendEmailCount: user.resendEmailCount || 0,
        resetPasswordRequestCount: user.resetPasswordRequestCount || 0,
        passwordUpdateCount: user.passwordUpdateCount || 0,
        signInCount: user.signInCount || 0,
        organizationDetailsRetrievalCount: user.organizationDetailsRetrievalCount || 0,
        childAccountsListRetrievalCount: user.childAccountsListRetrievalCount || 0,
        
        // Security Information
        lastPasswordChanged: user.lastPasswordChanged || null,
        loginAttempts: user.loginAttempts || 0,
        lastLoginAttempt: user.lastLoginAttempt || null,
        
        // Google SSO Configuration (for parent accounts)
        ...(user.accountType === 'parent' && user.googleSsoConfig && {
          googleSsoConfig: {
            enabled: user.googleSsoConfig.enabled || false,
            clientId: user.googleSsoConfig.clientId || ''
            // Note: clientSecret is intentionally excluded for security
          }
        }),
        
        // Notification Timestamps
        lastLowBalanceNotificationAt: user.lastLowBalanceNotificationAt || null,
        lastCriticalBalanceNotificationAt: user.lastCriticalBalanceNotificationAt || null,
        lastDepletedBalanceNotificationAt: user.lastDepletedBalanceNotificationAt || null,
        
        // Verification and Reset Information
        verificationToken: user.verificationToken || null,
        verificationTokenExpiry: user.verificationTokenExpiry || null,
        lastVerificationTokenSentAt: user.lastVerificationTokenSentAt || null,
        resetPasswordOTPExpiry: user.resetPasswordOTPExpiry || null,
        lastResetPasswordRequestAt: user.lastResetPasswordRequestAt || null,
        
        // Account Lock Information
        lockedUntil: user.lockedUntil || null,
        
        // Additional Fields
        secretKey: user.secretKey || null,
        iv: user.iv || null
      }
    }), event);

  } catch (error) {
    console.error('[GET-USER-PROFILE] Error retrieving user profile:', error);
    
    // Use config to determine if we should show detailed errors
    const errorMessage = config.NODE_ENV === 'local' 
      ? `${error instanceof Error ? error.message : 'An internal server error occurred'}` 
      : 'An internal server error occurred';
    
    return addCorsHeaders(ErrorTypes.INTERNAL_ERROR(errorMessage), event);
  }
};

// Export for SAM template compatibility
export const lambda = getUserProfileLambda;
